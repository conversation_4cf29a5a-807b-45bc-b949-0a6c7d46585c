# Application Settings
ENV=
APP_NAME=api-gateway
DEBUG=true
API_V1_STR=/api/v1

# Service endpoins
# Redis settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Rate limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# JWT settings
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS settings
CORS_ORIGINS="http://localhost,http://localhost:3000,http://localhost:8000"
CORS_CREDENTIALS=true
CORS_METHODS=["*"]
CORS_HEADERS=["*"]

# Service discovery settings
SERVICE_DISCOVERY_ENABLED=false
SERVICE_DISCOVERY_HOST=consul
SERVICE_DISCOVERY_PORT=8500

# Proto
REPO_URL=
GIT_TOKEN=

FRONTEND_AUTH_URL=

# Google OAuth
GOOGLE_CLIENT_ID="google-client-id"
GOOGLE_CLIENT_SECRET="google-client-secret"
GOOGLE_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# GitHub OAuth
GITHUB_CLIENT_ID="github-client-id"
GITHUB_CLIENT_SECRET="github-client-secret"
GITHUB_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

CUSTOM_OAUTH_PROVIDERS="{}"

# Kafka Settings
KAFKA_BROKER_PORT=9092
KAFKA_BROKER_HOST=localhost


