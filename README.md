# New FastAPI Project

A FastAPI project following RUH AI standards and folder structure.

## Project Structure

```
new-fastapi-project/
├── app/
│   ├── api/
│   │   └── routers/
│   ├── core/
│   ├── schemas/
│   ├── services/
│   ├── shared/
│   ├── utils/
│   └── main.py
├── tests/
├── docs/
├── pyproject.toml
├── Dockerfile
├── pytest.ini
├── run_local.sh
└── run_tests.sh
```

## Setup

1. Install dependencies:
   ```bash
   poetry install
   ```

2. Run the application locally:
   ```bash
   ./run_local.sh
   ```

3. Run tests:
   ```bash
   ./run_tests.sh
   ```

## Development

- Follow the existing code standards and patterns
- Use Test Driven Development (TDD)
- Maintain 100% test coverage
- Use poetry for dependency management

## API Documentation

Once running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
