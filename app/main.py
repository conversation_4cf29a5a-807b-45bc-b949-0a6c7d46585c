import os
import sys
import grpc
import logging
import threading
from concurrent import futures
from app.services.notification_service import NotificationService
from app.grpc import notification_pb2_grpc  # Updated import
from app.utils.kafka.kafka_consumer import KafkaConsumerService
from app.core.config import settings

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def start_kafka_consumer():
    """Run Kafka consumer in a separate thread"""
    logger.info("Starting Kafka consumer service...")
    try:
        consumer_service = KafkaConsumerService()
        consumer_service.consume_messages()
    except Exception as e:
        logger.error(f"Kafka consumer failed: {e}", exc_info=True)
    finally:
        if consumer_service:
            consumer_service.close()

def serve():
    """Initialize and start the gRPC server"""
    try:
        # Create gRPC server
        server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
        logger.debug("Created gRPC server")

        # Add notification service to server
        notification_service = NotificationService()
        notification_pb2_grpc.add_NotificationServiceServicer_to_server(  # Updated
            notification_service, server
        )
        logger.debug("Added NotificationService to server")

        # Get port
        port = settings.PORT
        server.add_insecure_port(f'[::]:{port}')
        
        # Start server
        server.start()
        logger.info(f"✅ gRPC server started on port {port}")

        # Start Kafka consumer in a separate thread
        kafka_thread = threading.Thread(
            target=start_kafka_consumer,
            daemon=True  # This ensures the thread stops when the main program stops
        )
        kafka_thread.start()
        logger.info("✅ Kafka consumer thread started")

        # Keep main thread alive
        server.wait_for_termination()

    except Exception as e:
        logger.critical(f"❌ Server failed: {e}", exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    logger.info("🚀 Starting notification service...")
    serve()
