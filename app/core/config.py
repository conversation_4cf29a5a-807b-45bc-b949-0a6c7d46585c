from typing import Any, Dict, Optional
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, validator


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "notification-microservice"
    DEBUG: bool = False
    PORT: int = 50060

    # Database settings
    DB_HOST: str
    DB_PORT: str
    DB_USER: str
    DB_PASSWORD: str
    DB_NAME: str
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None

    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v

        required = {"DB_USER", "DB_PASSWORD", "DB_HOST", "DB_PORT", "DB_NAME"}
        missing = required - values.keys()
        if missing:
            raise ValueError(f"Missing required database configuration: {missing}")

        return PostgresDsn.build(
            scheme="postgresql",
            username=values.get("DB_USER"),
            password=values.get("DB_PASSWORD"),
            host=values.get("DB_HOST"),
            port=int(values.get("DB_PORT", 5432)),
            path=f"{values.get('DB_NAME')}",
        )

    # Kafka Settings (Assuming these are already defined)
    BOOTSTRAP_SERVERS: str = ""

    # SendGrid Settings
    SENDGRID_API_KEY: str = ""
    SENDER_EMAIL: str = ""

    # Consumer Settings
    KAFKA_CONSUMER_GROUP_ID: str = ""

    # Proto
    REPO_URL: str = ""
    GIT_TOKEN: str = ""

    # Firebase Settings
    FIREBASE_CREDENTIALS: str

    @validator("FIREBASE_CREDENTIALS")
    def validate_firebase_credentials(cls, v: Optional[str]) -> str:
        if not v:
            raise ValueError("FIREBASE_CREDENTIALS must be set")
        return v

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
