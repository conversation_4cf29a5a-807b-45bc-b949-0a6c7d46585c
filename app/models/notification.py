from sqlalchemy import Column, String, Boolean, DateTime, text
from sqlalchemy.orm import declarative_base
from datetime import datetime

Base = declarative_base()

class Notification(Base):
    __tablename__ = "notifications"
    id = Column(String, primary_key=True)
    title = Column(String, nullable=False)
    user_id = Column(String, nullable=False)
    link = Column(String, nullable=True)
    logo = Column(String, nullable=True)
    seen = Column(Boolean, nullable=False, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)