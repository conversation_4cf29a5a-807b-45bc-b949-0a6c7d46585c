from fastapi import APIRouter
from typing import Dict, Any

health_router = APIRouter(tags=["health"])


@health_router.get(
    "/health",
    summary="Get basic health status",
    description="Returns a simple health status of the FastAPI application",
    response_model=Dict[str, str]
)
async def health_check() -> Dict[str, str]:
    """Basic health check endpoint."""
    return {"status": "healthy"}


@health_router.get(
    "/health/detailed",
    summary="Get detailed health status",
    description="Returns detailed health information about the application",
    response_model=Dict[str, Any]
)
async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check endpoint."""
    return {
        "status": "healthy",
        "service": "new-fastapi-project",
        "version": "0.1.0",
        "timestamp": "2024-01-01T00:00:00Z"
    }
