# email_service/kafka_consumer_service.py
import logging
import json
import time
import sys
from typing import Optional
from kafka import KafkaConsumer
from kafka.errors import NoBrokersAvailable
from app.core.config import settings
from app.services.notification_service import NotificationManagement
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.utils.sendgrid.sendgrid import EmailSender
from app.utils.fcm.fcm_config import FCMNotificationService

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class KafkaConsumerService:
    """
    Listens to Kafka topics and triggers email sending based on messages.
    Handles potentially double-encoded JSON messages and multiple action types.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.bootstrap_servers = settings.BOOTSTRAP_SERVERS
        self.topics = SendEmailTypeEnum.get_topics()
        self.group_id = settings.KAFKA_CONSUMER_GROUP_ID
        self._consumer: Optional[KafkaConsumer] = None
        self.email_sender = EmailSender()
        self.fcm_service = FCMNotificationService()
        self.notification_service = NotificationManagement()
        self.logger.info(f"KafkaConsumerService initializing for topics: {self.topics} and group: {self.group_id}")

    def _connect_consumer(self) -> Optional[KafkaConsumer]:
        """Establishes connection to Kafka."""
        if self._consumer:
             return self._consumer

        self.logger.info(f"Attempting to connect to Kafka brokers: {self.bootstrap_servers}")
        try:
            # The initial value_deserializer will attempt the first json.loads
            self._consumer = KafkaConsumer(
                *self.topics,
                bootstrap_servers=self.bootstrap_servers,
                group_id=self.group_id,
                # This first deserializer handles bytes -> potentially JSON string or dict
                value_deserializer=lambda v: json.loads(v.decode('utf-8')),
                key_deserializer=lambda k: k.decode('utf-8') if k else None,
                auto_offset_reset='earliest',
                enable_auto_commit=True,
                # Add security settings if needed...
            )
            self.logger.info("Successfully connected to Kafka.")
            return self._consumer
        except NoBrokersAvailable:
            self.logger.error(f"Could not connect to Kafka brokers at {self.bootstrap_servers}. Check connection and settings.")
            return None
        except Exception as e:
            self.logger.error(f"An unexpected error occurred while connecting to Kafka: {e}", exc_info=True)
            return None

    def consume_messages(self):
        """Continuously polls Kafka for messages and processes them."""
        retry_delay = 5
        max_retries = 12
        retries = 0

        while True:
            if not self._consumer:
                self.logger.info(f"Consumer not connected. Attempting connection (retry {retries+1}/{max_retries})...")
                self._connect_consumer()
                if not self._consumer:
                    if retries >= max_retries:
                        self.logger.error("Max connection retries reached. Stopping.")
                        sys.exit(1)
                    retries += 1
                    self.logger.info(f"Connection failed. Retrying in {retry_delay} seconds.")
                    time.sleep(retry_delay)
                    continue
                else:
                    retries = 0

            self.logger.info("Starting message consumption loop...")
            try:
                for message in self._consumer:
                    self.logger.debug(f"Received message: Topic={message.topic}, Partition={message.partition}, Offset={message.offset}, Key={message.key}, Raw Value Type={type(message.value)}")
                    self.logger.info(f"Processing message from topic: {message.topic}")

                    processed_value = None # Variable to hold the final dictionary
                    raw_value = message.value # Value after initial deserialization

                    try:
                        # --- Start Handling Double Encoding ---
                        if isinstance(raw_value, str):
                            # If it's still a string, the producer likely double-encoded. Parse again.
                            self.logger.debug(f"Raw value is a string, attempting secondary JSON parse: {raw_value[:100]}...") # Log snippet
                            try:
                                processed_value = json.loads(raw_value)
                                # Add detailed logging of the processed message
                                self.logger.info("Received Kafka message:")
                                self.logger.info(f"  Topic: {message.topic}")
                                self.logger.info(f"  Action: {processed_value.get('action')}")
                                self.logger.info(f"  Data: {json.dumps(processed_value.get('data', {}), indent=2)}")
                                
                                if not isinstance(processed_value, dict):
                                     self.logger.warning(f"Secondary parse of string did not result in a dictionary. Type: {type(processed_value)}, Value: {processed_value}")
                                     continue
                            except json.JSONDecodeError:
                                self.logger.error(f"Failed to perform secondary JSON parse on string value: {raw_value}", exc_info=True)
                                continue # Skip this malformed message
                        elif isinstance(raw_value, dict):
                            # If it's already a dict, the initial deserializer worked (producer might be fixed)
                             self.logger.debug("Raw value is already a dictionary.")
                             processed_value = raw_value
                        else:
                            # Unexpected type after initial deserialization
                            self.logger.warning(f"Skipping message with unexpected value type after initial deserialization: {type(raw_value)}, Value: {raw_value}")
                            continue
                        # --- End Handling Double Encoding ---

                        # Now, proceed with the dictionary 'processed_value'
                        if not isinstance(processed_value, dict) or "action" not in processed_value or "data" not in processed_value:
                             self.logger.warning(f"Skipping message - final value is not a valid dict or missing keys: {processed_value}")
                             continue # Skip this message

                        actions = processed_value.get("action")
                        data = processed_value.get("data", {})
                        
                        # Handle both string and list type actions
                        if isinstance(actions, str):
                            actions = [actions]
                        elif not isinstance(actions, list):
                            self.logger.warning(f"Invalid action type: {type(actions)}. Expected string or list.")
                            continue

                        email_type = message.topic # Topic name is the email type

                        # Process each action in the list
                        for action in actions:
                            try:
                                self.logger.info(f"Processing action: {action}")

                                if action == "sendEmail":
                                    try:
                                        email = data.get("emailId")
                                        name = data.get("userName")
                                        otp = data.get("otp")
                                        
                                        if not all([email, name]):
                                            self.logger.warning(f"Skipping 'sendEmail' action with missing required fields: {data}")
                                            continue
                                        
                                        # Trigger regular email sending
                                        self.logger.debug(f"Calling send_email for: Type={email_type}, Email={email}, Name={name}")
                                        self.email_sender.send_email(
                                            email_type=email_type,
                                            recipient_email=email,
                                            recipient_name=name,
                                            otp=otp
                                        )
                                        self.logger.info(f"Successfully processed sendEmail action for {email} from topic {email_type}")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'sendEmail' action: {e}", exc_info=True)
                                
                                elif action == "sendOrgEmail":
                                    try:
                                        email = data.get("emailId")
                                        name = data.get("userName")
                                        org_name = data.get("orgName")
                                        
                                        if not all([email, name, org_name]):
                                            self.logger.warning(f"Skipping 'sendOrgEmail' action with missing required fields: {data}")
                                            continue
                                        
                                        # Trigger organization email sending
                                        self.logger.debug(f"Calling send_org_email for: Type={email_type}, Email={email}, Name={name}, Org={org_name}")
                                        self.email_sender.send_org_email(
                                            email_type=email_type,
                                            owner_email=email,
                                            owner_name=name,
                                            organization=org_name
                                        )
                                        self.logger.info(f"Successfully processed sendOrgEmail action for {email} from topic {email_type}")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'sendOrgEmail' action: {e}", exc_info=True)

                                elif action == "sendEmailToMember":
                                    try:
                                        email = data.get("email")
                                        name = data.get("name")
                                        org_name = data.get("org_name")
                                        department = data.get("department")
                                        role = data.get("role") 
                                        link = data.get("link")
                                        
                                        if not all([email, org_name]):
                                            self.logger.warning(f"Skipping 'sendEmailToMember' action with missing required fields: {data}")
                                            continue
                                        
                                        # Trigger organization email sending
                                        self.logger.debug(f"Calling send_email_members for: Type={email_type}, Email={email}, Name={name}, Org={org_name}")
                                        self.email_sender.send_email_invitation(
                                            email_type=email_type,
                                            member_email=email,
                                            member_name=name,
                                            organization=org_name,
                                            department=department,
                                            role=role,
                                            invitation_link=link
                                        )
                                        self.logger.info(f"Successfully processed sendEmailToMember action for {email} from topic {email_type}")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'sendEmailToMember' action: {e}", exc_info=True)
                                
                                elif action == "addToWaitlist":
                                    try:
                                        email = data.get("emailId")
                                        
                                        if not email:
                                            self.logger.warning(f"Skipping 'addToWaitlist' action with missing required fields: {data}")
                                            continue
                                        
                                        # Trigger waitlist email sending
                                        self.logger.debug(f"Calling send_waitlist_email for: Type={email_type}, Email={email}")
                                        self.email_sender.send_waitlist_email(
                                            email_type=email_type,
                                            user_email=email
                                        )
                                        self.logger.info(f"Successfully processed addToWaitlist action for {email} from topic {email_type}")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'addToWaitlist' action: {e}", exc_info=True)
                                
                                elif action == "markedAsApproved":
                                    try:
                                        email = data.get("emailId")
                                        
                                        if not email:
                                            self.logger.warning(f"Skipping 'markedAsApproved' action with missing required fields: {data}")
                                            continue
                                        
                                        # Trigger waitlist approval email sending
                                        self.logger.debug(f"Calling send_waitlist_approval_email for: Type={email_type}, Email={email}")
                                        self.email_sender.send_approved_email(
                                            email_type=SendEmailTypeEnum.USER_APPROVED.value,
                                            user_email=email
                                        )
                                        self.logger.info(f"Successfully processed markedAsApproved action for {email} from topic {email_type}")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'markedAsApproved' action: {e}", exc_info=True)

                                elif action == "sendAgentEmail":
                                    try:
                                        # Extract common fields
                                        email = data.get("emailId")
                                        name = data.get("userName")
                                        agent_name = data.get("agentName")
                                        
                                        if not all([email, name, agent_name]):
                                            self.logger.warning(f"Skipping 'sendAgentEmail' action with missing required fields: {data}")
                                            continue
                                        
                                        
                                        # Send the email using the appropriate template
                                        self.logger.debug(f"Sending agent email for: Type={email_type}, Email={email}, Agent={agent_name}")
                                        self.email_sender.send_agent_email(
                                            email_type=email_type,
                                            recipient_email=email,
                                            recipient_name=name,
                                            agent_name=agent_name
                                        )
                                        
                                        self.logger.info(f"Successfully processed sendAgentEmail action for {email} regarding agent {agent_name}")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'sendAgentEmail' action: {e}", exc_info=True)
                                        
                                elif action == "sendWorkflowEmail":
                                    try:
                                        # Extract common fields
                                        email = data.get("emailId")
                                        name = data.get("userName")
                                        workflow_name = data.get("workflowName")
                                        
                                        if not all([email, name, workflow_name]):
                                            self.logger.warning(f"Skipping 'sendWorkflowEmail' action with missing required fields: {data}")
                                            continue
                                        
                                        # Send the email using the appropriate template
                                        self.logger.debug(f"Sending workflow email for: Type={email_type}, Email={email}, Workflow={workflow_name}")
                                        self.email_sender.send_workflow_email(
                                            email_type=email_type,
                                            recipient_email=email,
                                            recipient_name=name,
                                            workflow_name=workflow_name
                                        )
                                        
                                        self.logger.info(f"Successfully processed sendWorkflowEmail action for {email} regarding workflow {workflow_name}")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'sendWorkflowEmail' action: {e}", exc_info=True)
                                        
                                elif action == "sendNotification":
                                    try:
                                        token = data.get("fcmToken")
                                        email = data.get("emailId")
                                        title = data.get("title")
                                        body = data.get("body")
                                        
                                        if not token:
                                            self.logger.warning(f"Skipping notification for user {email}: No FCM token provided")
                                            continue
                                        
                                        self.notification_service.create_notification(
                                            data=data
                                        )
                                        
                                        self.logger.info(f"Successfully saved notification message for {email} from topic {email_type}")
                                        
                                        success, msg = self.fcm_service.send_notification(token, title, body)
                                        if not success:
                                            self.logger.error(f"Failed to send notification to user {email}: {msg}")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'sendNotification' action: {e}", exc_info=True)
                                
                                elif action == "sendMulticast":
                                    try:
                                        tokens = data.get("tokens", [])
                                        title = data.get("title")
                                        body = data.get("body")
                                        custom_data = data.get("data", {})
                                        
                                        response = self.fcm_service.send_multicast(tokens, title, body, custom_data)
                                        if not response:
                                            self.logger.error("Failed to send multicast notification")
                                        else:
                                            self.logger.info("Successfully sent multicast notification")
                                    
                                    except Exception as e:
                                        self.logger.error(f"Error processing 'sendMulticast' action: {e}", exc_info=True)
                                
                                elif action == "sendMCPEmail":
                                    try:
                                        # Extract common fields
                                        email = data.get("emailId")
                                        name = data.get("userName")
                                        mcp_name = data.get("mcpName")
                                        
                                        if not all([email, name, mcp_name]):
                                            self.logger.warning(f"Skipping 'sendMCPEmail' action with missing required fields: {data}")
                                            continue
                                        
                                        # Send the email using the appropriate template
                                        self.logger.debug(f"Sending MCP email for: Type={email_type}, Email={email}, MCP={mcp_name}")
                                        self.email_sender.send_mcp_email(
                                            email_type=email_type,
                                            recipient_email=email,
                                            recipient_name=name,
                                            mcp_name=mcp_name
                                        )
                                        
                                        self.logger.info(f"Successfully processed sendMCPEmail action for {email} regarding MCP {mcp_name}")
                                    except Exception as e:
                                        self.logger.error(f"Error processing sendMCPEmail action: {str(e)}", exc_info=True)
                                
                                else:
                                    self.logger.warning(f"Skipping message with unknown action: {action}")
                                    continue

                            except Exception as e:
                                self.logger.error(f"Error processing action '{action}': {e}", exc_info=True)
                                # Continue processing other actions even if one fails
                                continue

                    except json.JSONDecodeError as e:
                         # This might catch errors from the *initial* deserializer if the bytes aren't valid JSON at all
                         self.logger.error(f"Failed to initially decode JSON message bytes. Error: {e}", exc_info=True)
                    except Exception as processing_error:
                         self.logger.error(f"Error processing message from topic {message.topic}: {processing_error}", exc_info=True)

            except NoBrokersAvailable:
                 self.logger.error("Lost connection to Kafka brokers during consumption. Attempting to reconnect...")
                 self.close()
                 time.sleep(retry_delay)
            except KeyboardInterrupt:
                self.logger.info("KeyboardInterrupt received. Shutting down...")
                break
            except Exception as e:
                self.logger.error(f"An unexpected error occurred in the consumption loop: {e}", exc_info=True)
                self.close()
                time.sleep(retry_delay * 2)

        self.close()
        self.logger.info("KafkaConsumerService stopped.")

    def close(self):
        """Closes the Kafka consumer."""
        if self._consumer:
            self.logger.info("Closing Kafka consumer connection...")
            try:
                self._consumer.close()
                self._consumer = None
                self.logger.info("Kafka consumer closed.")
            except Exception as e:
                 self.logger.error(f"Error closing Kafka consumer: {e}", exc_info=True)
