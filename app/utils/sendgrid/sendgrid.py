import logging
import os
import sendgrid
from sendgrid.helpers.mail import Mail, Email, To, Content
from jinja2 import Environment, FileSystemLoader
from app.core.config import settings
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum

class EmailSender:
    """Handles sending emails using SendGrid and Handlebars templates."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.sg = sendgrid.SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
        self.from_email = Email(settings.SENDER_EMAIL)
        self.template_env = Environment(loader=FileSystemLoader("app/utils/email_templates"))
        self.logger.info("EmailSender initialized.")

    def _render_template(self, email_type_str: str,context : dict) -> tuple[str, str]:
        """Renders the template for the given email type."""
        subject_map = {
            SendEmailTypeEnum.WELCOME.value: "Welcome to Our Platform! Your Journey Begins Here",
            SendEmailTypeEnum.EMAIL_VERIFICATION.value: "Verify Your Email to Activate Your Account",
            SendEmailTypeEnum.FORGOT_PASSWORD.value: "Password Reset Request - Secure Your Account",
            SendEmailTypeEnum.ORG_CREATED.value: "Organization Created Successfully",
            SendEmailTypeEnum.ORG_UPDATED.value: "Organization Updated Successfully",
            SendEmailTypeEnum.ORG_DELETED.value: "Organization Deleted Successfully",
            SendEmailTypeEnum.ORG_USER_ADDED.value: "You Have been added in organization",
            SendEmailTypeEnum.ORG_USER_REMOVED.value: "You have been removed from organization",
            SendEmailTypeEnum.ADD_TO_WAITLIST.value: "You have been added in waitlist",
            SendEmailTypeEnum.USER_APPROVED.value: "You have been approved in waitlist",
            SendEmailTypeEnum.AGENT_CREATED.value: "Agent Created Successfully",
            SendEmailTypeEnum.AGENT_UPDATED.value: "Agent Updated Successfully",
            SendEmailTypeEnum.AGENT_DELETED.value: "Agent Deleted Successfully",
            SendEmailTypeEnum.WORKFLOW_CREATED.value: "Workflow Created Successfully",
            SendEmailTypeEnum.WORKFLOW_UPDATED.value: "Workflow Updated Successfully",
            SendEmailTypeEnum.WORKFLOW_DELETED.value: "Workflow Deleted Successfully",
            SendEmailTypeEnum.MCP_CREATED.value: "MCP Created Successfully",
            SendEmailTypeEnum.MCP_UPDATED.value: "MCP Updated Successfully",
            SendEmailTypeEnum.MCP_DELETED.value: "MCP Deleted Successfully",
            SendEmailTypeEnum.INVITE_MEMBER.value: "You have been invited to join an organization",
        }

        subject = subject_map.get(email_type_str, "Important Notification from Our Platform")

        try:
            template = self.template_env.get_template(f"{email_type_str.lower()}.hbs")
            # html_content = template.render(name=name, otp=otp)
            html_content = template.render(**context)
        except Exception as e:
            self.logger.error(f"Error rendering template {email_type_str}: {e}", exc_info=True)
            # Fallback HTML content when template rendering fails
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <body style="font-family: sans-serif; line-height: 1.6;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2>Hello {context.get('name')},</h2>
                    <p>You have a new notification from our platform.</p>
                    <p>If you were expecting an email verification or password reset, please contact our support team.</p>
                    <p>Best regards,<br>The Team</p>
                </div>
            </body>
            </html>
            """
        return subject, html_content


    def send_email(self, email_type: str, recipient_email: str, recipient_name: str, otp: str):
        """Sends an email using SendGrid and Handlebars templates."""
        self.logger.debug(f"Preparing to send email. Type: {email_type}, To: {recipient_email}, Name: {recipient_name}")

        try:
            subject, html_content_str = self._render_template(email_type, context={"name": recipient_name, "otp": otp})
            to_email = To(recipient_email)
            content = Content("text/html", html_content_str)
            mail = Mail(self.from_email, to_email, subject, content)

            response = self.sg.client.mail.send.post(request_body=mail.get())

            self.logger.info(f"Email sent to {recipient_email}. Type: {email_type}. Status Code: {response.status_code}")
            if response.status_code >= 300:
                self.logger.error(f"SendGrid error. Status: {response.status_code}, Body: {response.body}, Headers: {response.headers}")

        except Exception as e:
            self.logger.error(f"Failed to send email to {recipient_email}. Type: {email_type}. Error: {str(e)}", exc_info=True)


    def send_org_email(self, email_type: str, owner_email: str, owner_name: str, organization: str):
        """Sends an email using SendGrid and Handlebars templates."""
        self.logger.debug(f"Preparing to send email. Type: {email_type}, To: {owner_email}, Name: {owner_name}")

        try:
            subject, html_content_str = self._render_template(email_type, owner_name, organization)
            to_email = To(owner_email)
            content = Content("text/html", html_content_str)
            mail = Mail(self.from_email, to_email, subject, content)

            response = self.sg.client.mail.send.post(request_body=mail.get())

            self.logger.info(f"Email sent to {owner_email}. Type: {email_type}. Status Code: {response.status_code}")
            if response.status_code >= 300:
                self.logger.error(f"SendGrid error. Status: {response.status_code}, Body: {response.body}, Headers: {response.headers}")

        except Exception as e:
            self.logger.error(f"Failed to send email to {owner_email}. Type: {email_type}. Error: {str(e)}", exc_info=True)


    def send_email_invitation(self, email_type: str, member_email: str, member_name: str,
                            organization: str, invitation_link: str, department: str = None, role: str = None
                            ):
        """Sends an organization invitation email."""
        self.logger.debug(f"Sending invitation email to: {member_email} for org: {organization}")
        try:
            context = {
                "name": member_name,
                "orgName": organization,
                "invitationLink": invitation_link,
                "department": department,
                "role": role
            }

            subject, html_content_str = self._render_template(email_type, context)
            to_email = To(member_email)
            content = Content("text/html", html_content_str)
            mail = Mail(self.from_email, to_email, subject, content)

            response = self.sg.client.mail.send.post(request_body=mail.get())
            self.logger.info(f"Invitation email sent to {member_email}. Status: {response.status_code}")

            if response.status_code >= 300:
                self.logger.error(f"SendGrid error: Status={response.status_code}, Body={response.body}")
        except Exception as e:
            self.logger.error(f"Failed to send invitation to {member_email}: {str(e)}", exc_info=True)

        
    def send_waitlist_email(self, email_type: str, user_email: str):
        """Sends an email using SendGrid and Handlebars templates."""
        self.logger.debug(f"Preparing to send email. Type: {email_type}, To: {user_email}")

        try:
            subject, html_content_str = self._render_template(email_type_str=email_type, name=user_email, otp=None)
            to_email = To(user_email)
            content = Content("text/html", html_content_str)
            mail = Mail(self.from_email, to_email, subject, content)

            response = self.sg.client.mail.send.post(request_body=mail.get())

            self.logger.info(f"Email sent to {user_email}. Type: {email_type}. Status Code: {response.status_code}")
            if response.status_code >= 300:
                self.logger.error(f"SendGrid error. Status: {response.status_code}, Body: {response.body}, Headers: {response.headers}")

        except Exception as e:
            self.logger.error(f"Failed to send email to {user_email}. Type: {email_type}. Error: {str(e)}", exc_info=True)


    def send_approved_email(self, email_type: str, user_email: str):
        """Sends an email using SendGrid and Handlebars templates."""
        self.logger.debug(f"Preparing to send email. Type: {email_type}, To: {user_email}")

        try:
            subject, html_content_str = self._render_template(email_type_str=email_type, name=user_email, otp=None)
            to_email = To(user_email)
            content = Content("text/html", html_content_str)
            mail = Mail(self.from_email, to_email, subject, content)

            response = self.sg.client.mail.send.post(request_body=mail.get())

            self.logger.info(f"Email sent to {user_email}. Type: {email_type}. Status Code: {response.status_code}")
            if response.status_code >= 300:
                self.logger.error(f"SendGrid error. Status: {response.status_code}, Body: {response.body}, Headers: {response.headers}")

        except Exception as e:
            self.logger.error(f"Failed to send email to {user_email}. Type: {email_type}. Error: {str(e)}", exc_info=True)


    def send_agent_email(self, email_type: str, recipient_email: str, recipient_name: str, agent_name: str):
        """Sends an email using SendGrid and Handlebars templates."""
        self.logger.debug(f"Preparing to send email. Type: {email_type}, To: {recipient_email}, Name: {recipient_name}")

        try:
            subject, html_content_str = self._render_template(
                email_type_str=email_type,
                name=recipient_name,
                otp=agent_name
            )
            to_email = To(recipient_email)
            content = Content("text/html", html_content_str)
            mail = Mail(self.from_email, to_email, subject, content)

            response = self.sg.client.mail.send.post(request_body=mail.get())

            self.logger.info(f"Email sent to {recipient_email}. Type: {email_type}. Status Code: {response.status_code}")
            if response.status_code >= 300:
                self.logger.error(f"SendGrid error. Status: {response.status_code}, Body: {response.body}, Headers: {response.headers}")

        except Exception as e:
            self.logger.error(f"Failed to send email to {recipient_email}. Type: {email_type}. Error: {str(e)}", exc_info=True)


    def send_workflow_email(self, email_type: str, recipient_email: str, recipient_name: str, workflow_name: str):
        """Sends an email using SendGrid and Handlebars templates for workflow events."""
        self.logger.debug(f"Preparing to send workflow email. Type: {email_type}, To: {recipient_email}, Name: {recipient_name}")

        try:
            subject, html_content_str = self._render_template(
                email_type_str=email_type,
                name=recipient_name,
                otp=workflow_name
            )
            to_email = To(recipient_email)
            content = Content("text/html", html_content_str)
            mail = Mail(self.from_email, to_email, subject, content)

            response = self.sg.client.mail.send.post(request_body=mail.get())

            self.logger.info(f"Workflow email sent to {recipient_email}. Type: {email_type}. Status Code: {response.status_code}")
            if response.status_code >= 300:
                self.logger.error(f"SendGrid error. Status: {response.status_code}, Body: {response.body}, Headers: {response.headers}")

        except Exception as e:
            self.logger.error(f"Failed to send workflow email to {recipient_email}. Type: {email_type}. Error: {str(e)}", exc_info=True)


    def send_mcp_email(self, email_type: str, recipient_email: str, recipient_name: str, mcp_name: str):
        """Sends an email using SendGrid and Handlebars templates for MCP events."""
        self.logger.debug(f"Preparing to send MCP email. Type: {email_type}, To: {recipient_email}, Name: {recipient_name}")

        try:
            subject, html_content_str = self._render_template(
                email_type_str=email_type,
                name=recipient_name,
                otp=mcp_name
            )
            to_email = To(recipient_email)
            content = Content("text/html", html_content_str)
            mail = Mail(self.from_email, to_email, subject, content)

            response = self.sg.client.mail.send.post(request_body=mail.get())

            self.logger.info(f"MCP email sent to {recipient_email}. Type: {email_type}. Status Code: {response.status_code}")
            if response.status_code >= 300:
                self.logger.error(f"SendGrid error. Status: {response.status_code}, Body: {response.body}, Headers: {response.headers}")

        except Exception as e:
            self.logger.error(f"Failed to send MCP email to {recipient_email}. Type: {email_type}. Error: {str(e)}", exc_info=True)
