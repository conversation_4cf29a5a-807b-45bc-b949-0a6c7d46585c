import os
import json
import base64
import logging
import firebase_admin
from firebase_admin import credentials, messaging
from firebase_admin.exceptions import FirebaseError
from typing import Dict, Any, Tuple, Optional, List
from app.core.config import settings

class FCMNotificationService:
    """Firebase Cloud Messaging service for handling web notifications."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._initialize_firebase()

    def _decode_service_account_credentials(self) -> Dict[str, Any]:
        """Decode base64 encoded service account credentials."""
        try:
            # Get base64 encoded credentials from environment
            encoded_creds = settings.FIREBASE_CREDENTIALS
            if not encoded_creds:
                raise ValueError("FIREBASE_CREDENTIALS environment variable is not set")

            # Decode base64 to JSON string
            decoded_bytes = base64.b64decode(encoded_creds)
            decoded_json = decoded_bytes.decode('utf-8')
            
            # Parse JSON string to dict
            return json.loads(decoded_json)
        except Exception as e:
            self.logger.error(f"Failed to decode Firebase credentials: {e}")
            raise

    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK with credentials."""
        try:
            if not firebase_admin._apps:  # Check if already initialized
                service_account = self._decode_service_account_credentials()
                cred = credentials.Certificate(service_account)
                firebase_admin.initialize_app(cred)
                self.logger.info("Firebase Admin SDK initialized successfully")
        except Exception as e:
            self.logger.error(f"Failed to initialize Firebase: {e}")
            raise

    def send_notification(self, registration_token: str, title: str, body: str, 
                         data: Dict[str, Any] = None) -> Tuple[bool, str]:
        """
        Send a notification to a single token.
        
        Args:
            registration_token: FCM token to send to
            title: Notification title
            body: Notification body
            data: Optional data payload
            
        Returns:
            Tuple of (success: bool, message: str)
        """
        try:
            message = messaging.Message(
                notification=messaging.Notification(title=title, body=body),
                token=registration_token,
                data=data if data else {}
            )
            response = messaging.send(message)
            self.logger.info(f'Successfully sent message to token {registration_token[:10]}... Response: {response}')
            return True, response
        except messaging.UnregisteredError:
            self.logger.warning(f'Token {registration_token[:10]}... is unregistered.')
            return False, "Token Unregistered"
        except Exception as e:
            self.logger.error(f'Error sending message to token {registration_token[:10]}...: {e}')
            return False, str(e)

    def send_multicast(self, tokens: List[str], title: str, body: str, 
                      data: Dict[str, Any] = None) -> Optional[messaging.BatchResponse]:
        """
        Send the same notification to multiple tokens.
        
        Args:
            tokens: List of FCM tokens to send to
            title: Notification title
            body: Notification body
            data: Optional data payload
            
        Returns:
            BatchResponse object if successful, None if failed
        """
        if not tokens:
            self.logger.warning("No tokens provided to send notifications to.")
            return None

        # FCM limit is 500 tokens per request
        tokens_list = tokens[:500]
        
        try:
            message = messaging.MulticastMessage(
                notification=messaging.Notification(title=title, body=body),
                tokens=tokens_list,
                data=data if data else {}
            )
            response = messaging.send_multicast(message)
            self.logger.info(f'Multicast completed. Success: {response.success_count}, Failure: {response.failure_count}')

            # Log failures without token management
            if response.failure_count > 0:
                for idx, resp in enumerate(response.responses):
                    if not resp.success:
                        token = tokens_list[idx]
                        self.logger.warning(f'Failed to send to token {token[:10]}... Reason: {resp.exception}')

            return response
        except Exception as e:
            self.logger.error(f'Error in multicast send: {e}')
            return None
