from enum import Enum

class SendEmailTypeEnum(Enum):
    WELCOME = "WELCOME"
    EMAIL_VERIFICATION = "EMAIL_VERIFICATION"
    FORGOT_PASSWORD = "FORGOT_PASSWORD"

    # New organization email types
    ORG_CREATED = "ORG_CREATED"
    ORG_UPDATED = "ORG_UPDATED" 
    ORG_DELETED = "ORG_DELETED"
    ORG_USER_ADDED = "ORG_USER_ADDED"
    ORG_USER_REMOVED = "ORG_USER_REMOVED"

    # New Waitlist email types
    ADD_TO_WAITLIST = "ADD_TO_WAITLIST"
    USER_APPROVED = "USER_APPROVED"

    # Agent email types
    AGENT_CREATED = "AGENT_CREATED"
    AGENT_UPDATED = "AGENT_UPDATED"
    AGENT_DELETED = "AGENT_DELETED"

    # Workflow email types
    WORKFLOW_CREATED = "WORKFLOW_CREATED"
    WORKFLOW_UPDATED = "WORKFLOW_UPDATED"
    WORKFLOW_DELETED = "WORKFLOW_DELETED"
    
    # mcp email types
    MCP_CREATED = "MCP_CREATED"
    MCP_UPDATED = "MCP_UPDATED"
    MCP_DELETED = "MCP_DELETED"
    
    
    INVITE_MEMBER= "INVITE_MEMBER"

    # Helper to get all topic values
    @classmethod
    def get_topics(cls):
        return [item.value for item in cls]