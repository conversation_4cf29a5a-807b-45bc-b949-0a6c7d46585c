<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Ruh AI - Your AI-Powered Digital Workforce</title>
    <link rel="icon" type="image/x-icon" href="https://ruh.ai/favicon.ico">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #121212;
            background-color: #fafafa;
            margin: 0;
            padding: 20px;
        }

        .email-container {
            max-width: 600px;
            width: 100%;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(229, 231, 235, 0.5);
        }

        .header {
            background-color: #ae00d0;
            padding: 40px 40px;
            text-align: center;
            min-height: 120px;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .logo-container {
            position: relative;
            z-index: 2;
            margin-bottom: 3px;
        }

        .logo-img {
            width: 64px;
            height: 64px;
            margin-bottom: 20px;
            filter: brightness(0) invert(1);
            display: block;
        }

        .header h1 {
            color: white;
            font-size: 36px;
            font-weight: 700;
            margin: 0;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            letter-spacing: -0.5px;
        }

        .header p {
            color: rgba(255, 255, 255, 0.95);
            font-size: 18px;
            margin-top: 12px;
            position: relative;
            z-index: 2;
            font-weight: 400;
        }

        .content {
            padding: 50px 40px;
            background-color: #ffffff;
        }

        .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #121212;
            margin-bottom: 16px;
        }

        .welcome-message {
            font-size: 16px;
            color: #4b5563;
            margin-bottom: 32px;
            line-height: 1.7;
        }

        .cta-button {
            display: inline-block;
            background-color: #ae00d0;
            color: white !important;
            text-decoration: none;
            padding: 18px 40px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 32px 0;
            border: none;
            min-width: 200px;
            letter-spacing: 0.5px;
        }

        .features-section {
            background-color: #fdf4ff;
            border-radius: 16px;
            padding: 40px;
            margin: 40px 0;
            border: 1px solid rgba(174, 0, 208, 0.1);
        }

        .features-title {
            font-size: 20px;
            font-weight: 600;
            color: #12195e;
            margin-bottom: 24px;
            text-align: center;
        }

        .features {
            margin: 0;
        }

        .feature {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .feature:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(174, 0, 208, 0.1);
            border-color: #ae00d0;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ae00d0 0%, #7b5aff 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            flex-shrink: 0;
        }

        .feature-content {
            flex: 1;
        }

        .feature-title {
            font-weight: 600;
            color: #121212;
            margin-bottom: 4px;
            font-size: 16px;
        }

        .feature-description {
            color: #4b5563;
            font-size: 14px;
            line-height: 1.5;
        }

        .stats-section {
            background: linear-gradient(135deg, #fdf4ff 0%, #fbfaff 100%);
            border-radius: 16px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
            border: 1px solid rgba(174, 0, 208, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 24px;
            margin-top: 24px;
        }

        .stat-item {
            background-color: white;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #ae00d0;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #4b5563;
        }

        .next-steps {
            background-color: #f8f8fa;
            border-radius: 16px;
            padding: 40px;
            margin: 40px 0;
            border: 1px solid rgba(229, 231, 235, 0.8);
        }

        .next-steps-title {
            font-size: 18px;
            font-weight: 600;
            color: #12195e;
            margin-bottom: 20px;
            text-align: center;
        }

        .footer {
            background-color: #fafafa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .footer-content {
            max-width: 400px;
            margin: 0 auto;
        }

        .footer-logo {
            margin-bottom: 16px;
        }

        .footer-text {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .footer-link {
            color: #ae00d0;
            text-decoration: none;
        }

        .footer-link:hover {
            text-decoration: underline;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-links a {
            display: inline-block;
            margin: 0 8px;
            color: #ae00d0;
            text-decoration: none;
            font-size: 14px;
        }

        /* Responsive Design */
        @media only screen and (max-width: 600px) {
            body {
                padding: 15px;
            }

            .email-container {
                border-radius: 12px;
                margin: 0 10px;
            }

            .header {
                padding: 40px 25px;
                min-height: 200px;
            }

            .content {
                padding: 40px 25px;
            }

            .footer {
                padding: 25px;
            }

            .header h1 {
                font-size: 30px;
            }

            .header p {
                font-size: 16px;
            }

            .cta-button {
                padding: 16px 30px;
                font-size: 15px;
                min-width: 180px;
            }

            .features-section,
            .stats-section,
            .next-steps {
                padding: 30px 25px;
                margin: 30px 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
</head>

<body>
    <div class="email-container">
        <div class="header" style="background-color: #ae00d0; padding: 40px 40px; text-align: center; min-height: 120px;">
            <h1 style="color: white; margin: 0 0 10px 0; font-size: 28px; font-weight: 700;">Welcome to Ruh AI! 🚀</h1>
            <p style="color: white; margin: 0; font-size: 16px; opacity: 0.9;">Your AI-powered digital workforce awaits</p>
        </div>

        <div class="content">
            <div class="greeting">Hello {{name}}! 👋</div>

            <div class="welcome-message">
                Welcome to <strong>Ruh AI</strong> - where the future of work begins! We're thrilled to have you join
                our community of innovators who are transforming how work gets done with AI agents and intelligent
                automation.
                <br><br>
                You're now part of a platform that's revolutionizing productivity through AI. Let's get you started on
                your journey to 10x faster work and 75% less busywork!
            </div>

            <div style="text-align: center;">
                <a href="https://ruh.ai/work-lab" class="cta-button" style="display: inline-block; background-color: #ae00d0; color: white !important; text-decoration: none; padding: 18px 40px; border-radius: 12px; font-weight: 600; font-size: 16px; text-align: center; margin: 32px 0; border: none; min-width: 200px; letter-spacing: 0.5px;">
                    🎯 Explore Your Dashboard
                </a>
            </div>

            <div class="stats-section">
                <h3 style="color: #12195e; margin-bottom: 8px;">Join thousands who are already automating their work
                </h3>
                <div class="stats-grid">

                    <div class="stat-item">
                        <div class="stat-number">10x</div>
                        <div class="stat-label">Faster Work</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3x</div>
                        <div class="stat-label">Revenue Growth</div>
                    </div>
                </div>
            </div>

            <div class="features-section">
                <div class="features-title">🎉 Here's what you can do right now:</div>

                <div class="features">
                    <div class="feature">
                        <div class="feature-icon">
                            <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
                                <path
                                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                            </svg>
                        </div>
                        <div class="feature-content">
                            <div class="feature-title">Meet Julia - Your AI Marketing Assistant</div>
                            <div class="feature-description">Start with our preset AI employee who can create blogs,
                                videos, and social media content 24/7.</div>
                        </div>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
                                <path
                                    d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z" />
                            </svg>
                        </div>
                        <div class="feature-content">
                            <div class="feature-title">Browse the AI Marketplace</div>
                            <div class="feature-description">Discover ready-to-use AI agents and workflows built by
                                experts. One-click import and customize to your needs.</div>
                        </div>
                    </div>

                    <div class="feature">
                        <div class="feature-icon">
                            <svg width="20" height="20" fill="white" viewBox="0 0 24 24">
                                <path
                                    d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                            </svg>
                        </div>
                        <div class="feature-content">
                            <div class="feature-title">Connect Your Knowledge Base</div>
                            <div class="feature-description">Upload your docs, connect your tools, and train AI agents
                                with your company's unique knowledge and processes.</div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 32px; color: #4b5563; text-align: center;">
                <p style="font-size: 16px; margin-bottom: 8px;">Ready to transform how you work?</p>
                <p style="margin-top: 8px;">
                    <strong style="color: #121212;">The Ruh AI Team</strong><br>
                    <span style="color: #ae00d0; font-style: italic;">Building the future of work, one agent at a
                        time</span>
                </p>
            </div>
        </div>

        <div class="footer">
            <div class="footer-content">
                <div class="footer-logo">
                    <strong style="color: #ae00d0; font-size: 18px;">Ruh AI</strong>
                </div>
                <div class="footer-text">
                    © 2025 Ruh AI Inc. All rights reserved.
                </div>
                <div class="footer-text">
                    Questions? Contact us at <a href="mailto:<EMAIL>" class="footer-link"><EMAIL></a>
                </div>
                <div class="social-links">
                    <a href="https://ruh.ai" class="footer-link">Website</a> •
                    <a href="https://x.com/ruhdotai" class="footer-link">Twitter</a> •
                    <a href="https://www.instagram.com/ruhdotai/" class="footer-link">Instagram</a>
                </div>
            </div>
        </div>
    </div>
</body>

</html>