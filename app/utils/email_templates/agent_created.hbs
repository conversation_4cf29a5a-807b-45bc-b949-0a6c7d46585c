<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Agent Created</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4F46E5;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .header h1 {
            color: white;
            margin: 0;
            font-size: 24px;
        }
        .content {
            background-color: #f9fafb;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            border: 1px solid #e5e7eb;
            border-top: none;
        }
        .agent-card {
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .agent-name {
            color: #4F46E5;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .cta-button {
            display: inline-block;
            background-color: #4F46E5;
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            margin: 15px 0;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
        }
        .success-icon {
            font-size: 48px;
            text-align: center;
            margin: 20px 0;
            color: #10B981;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>New AI Agent Created! 🎉</h1>
    </div>
    
    <div class="content">
        <p>Hello {{name}},</p>
        
        <div class="agent-card">
            <div class="agent-name">{{otp}}</div>
            <p>Your new AI agent has been successfully created and is ready to assist you!</p>
        </div>

        <p>You can now:</p>
        <ul>
            <li>Configure your agent's settings</li>
            <li>Train it with custom knowledge</li>
            <li>Start interacting with your agent</li>
            <li>Monitor its performance</li>
        </ul>
        
        <a href="#" class="cta-button">Start Using Your Agent</a>
        
        <p>If you need any help getting started, our documentation and support team are here to help.</p>
        
        <p>Best regards,<br>The Ruh.ai Team</p>
        
        <div class="footer">
            <p>© 2025 Ruh.ai. All rights reserved.</p>
        </div>
    </div>
</body>
</html>