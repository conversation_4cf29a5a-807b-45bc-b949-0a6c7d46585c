<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reset Your Password - Ruh AI</title>
  <link rel="icon" type="image/x-icon" href="https://ruh.ai/favicon.ico">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #121212;
      background-color: #fafafa;
      margin: 0;
      padding: 20px;
    }

    .email-container {
      max-width: 600px;
      width: 100%;
      margin: 0 auto;
      background-color: #ffffff;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      border: 1px solid rgba(229, 231, 235, 0.5);
    }

    .header {
      background-color: #ae00d0;
      padding: 40px 40px;
      text-align: center;
      min-height: 120px;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .logo-container {
      position: relative;
      z-index: 2;
      margin-bottom: 24px;
    }

    .logo-img {
      width: 64px;
      height: 64px;
      margin-bottom: 20px;
      filter: brightness(0) invert(1);
      display: block;
    }

    .header h1 {
      color: white;
      font-size: 32px;
      font-weight: 700;
      margin: 0;
      position: relative;
      z-index: 2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      letter-spacing: -0.5px;
    }

    .header p {
      color: rgba(255, 255, 255, 0.95);
      font-size: 18px;
      margin-top: 12px;
      position: relative;
      z-index: 2;
      font-weight: 400;
    }

    .content {
      padding: 50px 40px;
      background-color: #ffffff;
      min-height: 400px;
    }

    .greeting {
      font-size: 18px;
      font-weight: 600;
      color: #121212;
      margin-bottom: 16px;
    }

    .message {
      font-size: 16px;
      color: #4b5563;
      margin-bottom: px;
      line-height: 1.7;
    }

    .reset-button {
      display: inline-block;
      background-color: #ae00d0;
      color: white !important;
      text-decoration: none;
      padding: 18px 40px;
      border-radius: 12px;
      font-weight: 600;
      font-size: 16px;
      text-align: center;
      margin: 40px 0;
      border: none;
      min-width: 200px;
      letter-spacing: 0.5px;
    }

    .link-container {
      background-color: #fdf4ff;
      border: 1px solid #e5e7eb;
      border-radius: 16px;
      padding: 30px;
      margin: 40px 0;
      text-align: center;
    }

    .reset-link {
      word-break: break-all;
      font-size: 13px;
      color: #ae00d0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      background-color: white;
      padding: 16px;
      border-radius: 10px;
      border: 1px solid #e5e7eb;
      margin-top: 16px;
      line-height: 1.4;
    }

    .expiry-note {
      background-color: #f7e6fa;
      border-left: 4px solid #ae00d0;
      padding: 16px 20px;
      margin: 24px 0;
      border-radius: 0 8px 8px 0;
      font-size: 14px;
      color: #12195e;
    }

    .security-tips {
      background-color: #f8f8fa;
      border-radius: 16px;
      padding: 30px;
      margin: 40px 0;
      border: 1px solid rgba(229, 231, 235, 0.8);
    }

    .security-tips h3 {
      color: #12195e;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    .security-tips ul {
      margin: 0;
      padding-left: 20px;
    }

    .security-tips li {
      color: #4b5563;
      margin-bottom: 8px;
      line-height: 1.5;
    }

    .security-note {
      background-color: #f8f8fa;
      border-radius: 12px;
      padding: 20px;
      margin: 24px 0;
      font-size: 14px;
      color: #4b5563;
      text-align: center;
    }

    .footer {
      background-color: #fafafa;
      padding: 30px;
      text-align: center;
      border-top: 1px solid #e5e7eb;
    }

    .footer-content {
      max-width: 400px;
      margin: 0 auto;
    }

    .footer-logo {
      margin-bottom: 16px;
    }

    .footer-text {
      font-size: 14px;
      color: #6b7280;
      margin-bottom: 8px;
    }

    .footer-link {
      color: #ae00d0;
      text-decoration: none;
    }

    .footer-link:hover {
      text-decoration: underline;
    }

    .social-links {
      margin-top: 20px;
    }

    .social-links a {
      display: inline-block;
      margin: 0 8px;
      color: #ae00d0;
      text-decoration: none;
      font-size: 14px;
    }

    /* Responsive Design */
    @media only screen and (max-width: 600px) {
      body {
        padding: 15px;
      }

      .email-container {
        border-radius: 12px;
        margin: 0 10px;
      }

      .header {
        padding: 40px 25px;
        min-height: 180px;
      }

      .content {
        padding: 40px 25px;
      }

      .footer {
        padding: 25px;
      }

      .header h1 {
        font-size: 26px;
      }

      .header p {
        font-size: 16px;
      }

      .reset-button {
        padding: 16px 30px;
        font-size: 15px;
        min-width: 180px;
      }

      .link-container,
      .security-tips {
        padding: 25px 20px;
        margin: 30px 0;
      }
    }
  </style>
</head>

<body>
  <div class="email-container">
    <div class="header" style="background-color: #ae00d0; padding: 40px 40px; text-align: center; min-height: 120px;">
      <h1 style="color: white; margin: 0 0 10px 0; font-size: 28px; font-weight: 700;">Reset Password 🔐</h1>
      <p style="color: white; margin: 0; font-size: 16px; opacity: 0.9;">Secure your Ruh AI account</p>
    </div>

    <div class="content">
      <div class="greeting">Hello {{name}}! 👋</div>

      <div class="message">
        We received a request to reset your password for your <strong>Ruh AI</strong> account. If you didn't make this
        request, you can safely ignore this email.
        <br><br>
        To reset your password and regain access to your AI-powered digital workforce, please click the button below:
      </div>

      <div style="text-align: center;">
        <a href="{{otp}}" class="reset-button" style="display: inline-block; background-color: #ae00d0; color: white !important; text-decoration: none; padding: 18px 40px; border-radius: 12px; font-weight: 600; font-size: 16px; text-align: center; margin: 40px 0; border: none; min-width: 200px; letter-spacing: 0.5px;">
          🔑 Reset My Password
        </a>
      </div>
      <div class="expiry-note">
        <strong>⏰ Security Notice:</strong> This password reset link will expire in 5 minutes for your security.
      </div>


      <div style="margin-top: 32px; color: #4b5563; text-align: center;">
        <p style="font-size: 16px; margin-bottom: 8px;">Need help with your account?</p>
        <p style="margin-top: 8px;">
          <strong style="color: #121212;">The Ruh AI Security Team</strong><br>
          <span style="color: #ae00d0; font-style: italic;">Protecting your AI workforce</span>
        </p>
      </div>
    </div>

    <div class="footer">
      <div class="footer-content">
        <div class="footer-logo">
          <strong style="color: #ae00d0; font-size: 18px;">Ruh AI</strong>
        </div>
        <div class="footer-text">
          © 2025 Ruh AI Inc. All rights reserved.
        </div>
        <div class="footer-text">
          For security reasons, this email was sent to the email address associated with your account.
        </div>
        <div class="footer-text">
          Need help? Contact us at <a href="mailto:<EMAIL>" class="footer-link"><EMAIL></a>
        </div>
        <div class="social-links">
          <a href="https://ruh.ai" class="footer-link">Website</a> •
          <a href="https://x.com/ruhdotai" class="footer-link">Twitter</a> •
          <a href="https://www.instagram.com/ruhdotai/" class="footer-link">Instagram</a>
        </div>
      </div>
    </div>
  </div>
</body>

</html>