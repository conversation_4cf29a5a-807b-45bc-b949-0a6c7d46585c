# app/services/agent_service.py
import grpc
import json
import structlog
import uuid
from app.db.session import SessionLocal
from app.models.notification import Notification
from typing import Any, Dict
import uuid
from datetime import datetime
import grpc
from sqlalchemy import func
from app.models.notification import Notification
from app.grpc import notification_pb2, notification_pb2_grpc
from app.db.session import SessionLocal
import math


logger = structlog.get_logger()


class NotificationManagement():
    """
    Service for managing agent configurations.
    
    This service handles CRUD operations for agent configurations, including
    validation, storage in Google Cloud Storage, and database operations.
    """
    def get_db(self):
        """
        Creates and returns a new database session.

        Returns:
            Session: SQLAlchemy database session
        """
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def create_notification(self, data: dict) -> None:
        """
        Creates a notification record in the database.
        
        Args:
            data: Dictionary containing notification data from Kafka message
        """
        try:
            db = self.get_db()
            notification = Notification(
                id=str(uuid.uuid4()),
                title=data.get("title"),
                user_id=data.get("userId"),
                link=data.get("link"),
                logo=data.get("logo"),
                seen=False
            )
            db.add(notification)
            db.commit()
            db.refresh(notification)
        except Exception as error:
            logger.error(f"Error creating notification: {error}", exc_info=True)
            db.rollback()
            raise error
        finally:
            db.close()


class NotificationService(notification_pb2_grpc.NotificationServiceServicer):
    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def GetUserNotifications(
        self, 
        request: notification_pb2.GetUserNotificationsRequest,
        context: grpc.ServicerContext
    ) -> notification_pb2.GetUserNotificationsResponse:
        """
        Retrieves a paginated list of notifications for a specific user.
        """
        logger.info(f"GetUserNotifications called for user_id: {request.user_id}")
        logger.debug(f"Request parameters - page: {request.page}, page_size: {request.page_size}")
        
        db = self.get_db()
        try:
            page = request.page
            page_size = request.page_size
            user_id = request.user_id

            # Get total count of notifications for the user
            total = db.query(Notification)\
                .filter(Notification.user_id == user_id)\
                .count()
            logger.debug(f"Total notifications found for user: {total}")

            # Get paginated notifications
            notifications = db.query(Notification)\
                .filter(Notification.user_id == user_id)\
                .order_by(Notification.created_at.desc())\
                .offset((page - 1) * page_size)\
                .limit(page_size)\
                .all()
            logger.debug(f"Retrieved {len(notifications)} notifications for current page")

            # Calculate total pages
            total_pages = (total + page_size - 1) // page_size
            logger.debug(f"Total pages calculated: {total_pages}")

            # Convert to proto messages
            notification_messages = [
                notification_pb2.Notification(
                    id=notif.id,
                    title=notif.title,
                    user_id=notif.user_id,
                    link=notif.link if notif.link else "",
                    logo=notif.logo if notif.logo else "",
                    seen=notif.seen,
                    created_at=notif.created_at.isoformat()
                ) for notif in notifications
            ]
            logger.info(f"Successfully processed notifications for user {user_id}")

            return notification_pb2.GetUserNotificationsResponse(
                notifications=notification_messages,
                total=total,
                total_pages=total_pages,
                page=page
            )

        except Exception as e:
            logger.error(f"Error in GetUserNotifications: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return notification_pb2.GetUserNotificationsResponse()
        finally:
            db.close()
            logger.debug("Database connection closed")

    def MarkNotificationAsSeen(
        self,
        request: notification_pb2.MarkNotificationAsSeenRequest,
        context: grpc.ServicerContext
    ) -> notification_pb2.MarkNotificationAsSeenResponse:
        logger.info(f"MarkNotificationAsSeen called for notification_id: {request.notification_id}")
        logger.debug(f"Request for user_id: {request.user_id}")
        
        try:
            db = self.get_db()
            
            # Find notification and verify ownership
            notification = db.query(Notification)\
                .filter(
                    Notification.id == request.notification_id,
                    Notification.user_id == request.user_id
                )\
                .first()

            if not notification:
                logger.warning(f"Notification not found or unauthorized. ID: {request.notification_id}, User: {request.user_id}")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Notification not found or unauthorized")
                return notification_pb2.MarkNotificationAsSeenResponse(
                    success=False,
                    message="Notification not found or unauthorized"
                )

            # Update seen status
            notification.seen = True
            db.commit()
            logger.info(f"Successfully marked notification {request.notification_id} as seen")
            print("Successfully marked notification")
            return notification_pb2.MarkNotificationAsSeenResponse(
                success=True,
                message="Notification marked as seen"
            )

        except Exception as e:
            logger.error(f"Error in MarkNotificationAsSeen: {str(e)}", exc_info=True)
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return notification_pb2.MarkNotificationAsSeenResponse(
                success=False,
                message="Failed to mark notification as seen"
            )
        finally:
            db.close()
            logger.debug("Database connection closed")

    def GetNotificationById(
        self,
        request: notification_pb2.GetNotificationByIdRequest,
        context: grpc.ServicerContext
    ) -> notification_pb2.GetNotificationByIdResponse:
        """
        Retrieves a specific notification by its ID.
        Also verifies that the requesting user has access to this notification.
        """
        logger.info(f"GetNotificationById called for notification_id: {request.notification_id}")
        logger.debug(f"Request for user_id: {request.user_id}")
        
        try:
            db = self.get_db()
            
            # Find notification and verify ownership
            notification = db.query(Notification)\
                .filter(
                    Notification.id == request.notification_id,
                    Notification.user_id == request.user_id
                )\
                .first()

            if not notification:
                logger.warning(f"Notification not found or unauthorized. ID: {request.notification_id}, User: {request.user_id}")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Notification not found or unauthorized")
                return notification_pb2.GetNotificationByIdResponse(
                    success=False,
                    message="Notification not found or unauthorized"
                )

            # Convert to proto message
            notification_message = notification_pb2.Notification(
                id=notification.id,
                title=notification.title,
                user_id=notification.user_id,
                link=notification.link if notification.link else "",
                logo=notification.logo if notification.logo else "",
                seen=notification.seen,
                created_at=notification.created_at.isoformat()
            )
            
            logger.info(f"Successfully retrieved notification {request.notification_id}")
            
            return notification_pb2.GetNotificationByIdResponse(
                notification=notification_message,
                success=True,
                message="Notification retrieved successfully"
            )

        except Exception as e:
            logger.error(f"Error in GetNotificationById: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return notification_pb2.GetNotificationByIdResponse(
                success=False,
                message="Failed to retrieve notification"
            )
        finally:
            db.close()
            logger.debug("Database connection closed")
