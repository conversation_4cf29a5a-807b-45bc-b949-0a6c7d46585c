from typing import Optional, List
import structlog
from datetime import datetime

from app.schemas.user import UserCreate, UserUpdate, UserResponse, UserInDB
from app.core.security import get_password_hash, verify_password

logger = structlog.get_logger()


class UserService:
    """Service for user-related operations."""
    
    # In a real application, this would interact with a database
    # For now, we'll use a simple in-memory store for demonstration
    _users_db: List[UserInDB] = []
    _next_id = 1
    
    @classmethod
    def create_user(cls, user_data: UserCreate) -> UserResponse:
        """
        Create a new user.
        
        Args:
            user_data: User creation data
            
        Returns:
            UserResponse: Created user information
            
        Raises:
            ValueError: If user with email already exists
        """
        # Check if user already exists
        if cls.get_user_by_email(user_data.email):
            raise ValueError("User with this email already exists")
        
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        now = datetime.utcnow()
        
        user_in_db = UserInDB(
            id=str(cls._next_id),
            email=user_data.email,
            full_name=user_data.full_name,
            is_active=user_data.is_active,
            hashed_password=hashed_password,
            created_at=now,
            updated_at=now
        )
        
        cls._users_db.append(user_in_db)
        cls._next_id += 1
        
        logger.info(f"Created user with email: {user_data.email}")
        
        return UserResponse(
            id=user_in_db.id,
            email=user_in_db.email,
            full_name=user_in_db.full_name,
            is_active=user_in_db.is_active,
            created_at=user_in_db.created_at,
            updated_at=user_in_db.updated_at
        )
    
    @classmethod
    def get_user_by_id(cls, user_id: str) -> Optional[UserResponse]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            UserResponse or None: User information if found
        """
        for user in cls._users_db:
            if user.id == user_id:
                return UserResponse(
                    id=user.id,
                    email=user.email,
                    full_name=user.full_name,
                    is_active=user.is_active,
                    created_at=user.created_at,
                    updated_at=user.updated_at
                )
        return None
    
    @classmethod
    def get_user_by_email(cls, email: str) -> Optional[UserInDB]:
        """
        Get user by email (internal use).
        
        Args:
            email: User email
            
        Returns:
            UserInDB or None: User information if found
        """
        for user in cls._users_db:
            if user.email == email:
                return user
        return None
    
    @classmethod
    def authenticate_user(cls, email: str, password: str) -> Optional[UserResponse]:
        """
        Authenticate user with email and password.
        
        Args:
            email: User email
            password: User password
            
        Returns:
            UserResponse or None: User information if authenticated
        """
        user = cls.get_user_by_email(email)
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        return UserResponse(
            id=user.id,
            email=user.email,
            full_name=user.full_name,
            is_active=user.is_active,
            created_at=user.created_at,
            updated_at=user.updated_at
        )
