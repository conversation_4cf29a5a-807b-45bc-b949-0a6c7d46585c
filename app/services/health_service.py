import psutil
import time
from typing import Dict, Any
from datetime import datetime
import structlog

logger = structlog.get_logger()


class HealthService:
    """Service for health check operations."""
    
    _start_time = time.time()
    
    @classmethod
    def get_basic_health(cls) -> Dict[str, str]:
        """
        Get basic health status.
        
        Returns:
            Dict[str, str]: Basic health information
        """
        return {"status": "healthy"}
    
    @classmethod
    def get_detailed_health(cls) -> Dict[str, Any]:
        """
        Get detailed health status including system metrics.
        
        Returns:
            Dict[str, Any]: Detailed health information
        """
        try:
            # Calculate uptime
            uptime = int(time.time() - cls._start_time)
            
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_usage = {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used
            }
            
            return {
                "status": "healthy",
                "service": "new-fastapi-project",
                "version": "0.1.0",
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "uptime": uptime,
                "memory_usage": memory_usage
            }
        except Exception as e:
            logger.error(f"Error getting detailed health: {e}")
            return {
                "status": "degraded",
                "service": "new-fastapi-project",
                "version": "0.1.0",
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "error": str(e)
            }
